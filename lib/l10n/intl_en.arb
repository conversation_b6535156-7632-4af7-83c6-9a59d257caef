{"preferredByGuests": "Preferred by guests", "ratingDescription": "This property is in the top {percent}% of similar properties, based on reviews and reliability data.", "viewAllReviewsWithCount": "Show all {count} reviews", "noReviewsYet": "No reviews yet", "beFirstToReviewThisPlace": "Be the first to review this place", "aboutThisPlace": "About this place", "showMore": "Show more", "showLess": "Show less", "whatThisPlaceOffers": "What this place offers", "showAllAmenities": "Show all amenities", "whereYoullBe": "Where you'll be", "cancellationPolicy": "Cancellation Policy", "cancellationRules": "Cancellation Rules", "superhost": "Superhost", "since": "Since", "change": "Change", "night": "night", "checkIn": "Check In", "checkOut": "Check Out", "cancel": "Cancel", "done": "Done", "selectDates": "Select Dates", "@@locale": "en", "settings": "Settings", "darkMode": "Dark Mode", "soundClick": "Click Sounds", "soundScroll": "Scroll Sounds", "language": "Language", "arabic": "Arabic", "english": "English", "skip": "<PERSON><PERSON>", "next": "Next", "start": "Start", "searchHint": "Search your favorite destination...", "filter": "Filter", "loading": "Loading...", "noResults": "No results found", "perNight": "SR / night", "wifi": "Wi-Fi", "addToFavorites": "Add to favorites", "appName": "Gather Point", "selectCity": "Select City", "detectingLocation": "Detecting location...", "locationPermissionError": "Please enable location permission to use the app", "browseReels": "<PERSON><PERSON><PERSON>", "discoverLatestVisualContent": "Discover Latest Visual Content", "exploreCategories": "Explore Categories", "nearbyPlaces": "Nearby Places", "popularDestinations": "Popular Destinations", "featuredPlaces": "Featured Places", "discoverMore": "Discover More", "viewAll": "View All", "bookNow": "Book Now", "pricePerNight": "Price per Night (SAR)", "guests": "Guests", "rooms": "Rooms", "bathrooms": "Bathrooms", "amenities": "Amenities", "location": "Location", "reviews": "Reviews", "rating": "Rating", "excellent": "Excellent", "veryGood": "Very Good", "good": "Good", "fair": "Fair", "poor": "Poor", "soundSettings": "Sound Settings", "customizeExperience": "Customize your app experience", "themeEnabled": "Dark theme enabled", "themeDisabled": "Light theme enabled", "soundClickEnabled": "Click sounds enabled", "soundClickDisabled": "Click sounds disabled", "soundScrollEnabled": "Scroll sounds enabled", "soundScrollDisabled": "Scroll sounds disabled", "createProperty": "Create Property", "propertyTitle": "Property Title", "propertyDescription": "Description", "selectCategory": "Select Category", "titleAndDescription": "Title & Description", "pickLocation": "Pick Location", "imageGallery": "Image Gallery", "addImage": "Add Image", "availableServices": "Available Services", "selectServices": "Select available services", "pricing": "Pricing", "dailyPrice": "Daily Price", "weeklyPrice": "Weekly Price", "monthlyPrice": "Monthly Price", "commission": "Commission", "bookingDetails": "Booking Details & Policies", "numberOfBathrooms": "Number of Bathrooms", "numberOfBedrooms": "Number of Bedrooms", "numberOfGuests": "Number of Guests", "bookingPolicy": "Booking Policy", "back": "Back", "submit": "Submit", "confirmSubmission": "Confirm Submission", "confirmSubmissionMessage": "Are you sure you want to submit this property?", "error": "Error", "ok": "OK", "propertyCreatedSuccessfully": "Property created successfully", "tapToUploadImages": "Tap to upload images", "latitude": "Latitude", "longitude": "Longitude", "enterPrice": "Enter price", "failedToLoadCategories": "Failed to load categories", "failedToLoadFacilities": "Failed to load facilities", "failedToCreateItem": "Failed to create item", "home": "Home", "search": "Search", "reels": "<PERSON><PERSON>", "profile": "Profile", "myBookings": "My Bookings", "myListings": "My Listings", "editProfile": "Edit Profile", "personalInfo": "Personal Information", "contactInfo": "Contact Information", "additionalInfo": "Additional Information", "fullName": "Full Name", "bio": "Bio", "email": "Email", "phone": "Phone", "gender": "Gender", "birthdate": "Birthdate", "male": "Male", "female": "Female", "notSpecified": "Not Specified", "selectBirthdate": "Select Birthdate", "saveChanges": "Save Changes", "profileImage": "Profile Image", "tapToChangeImage": "Tap to change image", "totalBookings": "Total Bookings", "confirmedBookings": "Confirmed Bookings", "totalProperties": "Total Properties", "totalViews": "Total Views", "totalReservations": "Total Reservations", "all": "All", "confirmed": "Confirmed", "pending": "Pending", "completed": "Completed", "cancelled": "Cancelled", "active": "Active", "inactive": "Inactive", "underReview": "Under Review", "totalPrice": "Total Price", "viewDetails": "View Details", "cancelBooking": "Cancel Booking", "rebookProperty": "Rebook Property", "editProperty": "Edit Property", "bedrooms": "Bedrooms", "propertyDetails": "Property Details", "hostMode": "Host Mode", "enableHostMode": "Enable host mode to manage your properties", "logout": "Logout", "appearance": "Appearance", "notifications": "Notifications", "privacy": "Privacy & Security", "about": "About App", "manageNotifications": "Manage notification settings", "privacySettings": "Privacy and security settings", "appInfo": "App information and version", "version": "Version", "appDescription": "Property booking and rental app", "noBookingsYet": "No bookings yet", "noBookingsSubtitle": "You haven't made any bookings yet", "exploreProperties": "Explore Properties", "noPropertiesYet": "No properties yet", "noPropertiesSubtitle": "Start by adding your first property", "addProperty": "Add Property", "searching": "Searching...", "tryDifferentKeywords": "Try searching with different keywords", "backToSearch": "Back to Search", "loadingReels": "Loading reels...", "failedToLoadReels": "Failed to load reels", "checkConnection": "Check your internet connection", "retry": "Retry", "shareProperty": "Share Property", "yearsHosting": "years hosting", "noAmenitiesListed": "No amenities listed", "mapView": "Map view", "guestReview": "Guest Review", "reserve": "Reserve", "bookingFee": "Booking fee", "serviceFee": "Service fee", "taxes": "Taxes", "total": "Total", "currentLocation": "Current Location", "searchPlaceholder": "Welcome... search for what you want", "categories": "Categories", "popularPlaces": "Popular Places", "seeAll": "See All", "views": "Views", "properties": "Properties", "reservations": "Reservations", "status": "Status", "price": "Price", "checkInDate": "Check-in Date", "checkOutDate": "Check-out Date", "propertyName": "Property Name", "propertyLocation": "Property Location", "totalAmount": "Total Amount", "bookingStatus": "Booking Status", "bookingDate": "Booking Date", "hostName": "Host Name", "contactHost": "Contact Host", "cancelReservation": "Cancel Reservation", "modifyReservation": "Modify Reservation", "leaveReview": "Leave Review", "downloadReceipt": "Download Receipt", "propertyType": "Property Type", "description": "Description", "rules": "Rules", "safetyFeatures": "Safety Features", "accessibility": "Accessibility", "nearbyAttractions": "Nearby Attractions", "transportation": "Transportation", "checkInInstructions": "Check-in Instructions", "houseRules": "House Rules", "importantInfo": "Important Information", "instantBook": "Instant Book", "verified": "Verified", "newListing": "New Listing", "rareFind": "Rare Find", "guestFavorite": "Guest Favorite", "topRated": "Top Rated", "luxuryStay": "<PERSON><PERSON><PERSON>", "budgetFriendly": "Budget Friendly", "familyFriendly": "Family Friendly", "petFriendly": "Pet Friendly", "workFriendly": "Work Friendly", "partyFriendly": "Party Friendly", "smokingAllowed": "Smoking Allowed", "noSmoking": "No Smoking", "freeWifi": "Free WiFi", "freeParking": "Free Parking", "pool": "Pool", "gym": "Gym", "spa": "Spa", "restaurant": "Restaurant", "bar": "Bar", "laundry": "<PERSON><PERSON><PERSON>", "kitchen": "Kitchen", "airConditioning": "Air Conditioning", "heating": "Heating", "tv": "TV", "workspace": "Workspace", "balcony": "Balcony", "garden": "Garden", "beachAccess": "Beach Access", "mountainView": "Mountain View", "cityView": "City View", "oceanView": "Ocean View", "lakeView": "Lake View", "gardenView": "Garden View", "streetView": "Street View", "noView": "No View", "bookingSummary": "Booking Summary", "hostDashboard": "Host Dashboard", "walletBalance": "Wallet Balance", "totalEarnings": "Total Earnings", "recentBookings": "Recent Bookings", "recentReviews": "Recent Reviews", "withdraw": "Withdraw", "earnings": "Earnings", "bookingsChart": "Bookings Chart", "earningsChart": "Earnings Chart", "thisMonth": "This Month", "lastMonth": "Last Month", "thisYear": "This Year", "availableBalance": "Available Balance", "pendingEarnings": "Pending Earnings", "totalWithdrawn": "Total Withdrawn", "withdrawFunds": "Withdraw Funds", "enterAmount": "Enter Amount", "minimumWithdraw": "Minimum withdrawal: SR 50", "withdrawalMethod": "<PERSON><PERSON><PERSON> Method", "bankTransfer": "Bank Transfer", "paypal": "PayPal", "processing": "Processing", "noRecentBookings": "No recent bookings", "noRecentReviews": "No recent reviews", "average": "Average", "guestName": "Guest Name", "checkInOut": "Check-in/out", "nightsStayed": "Nights Stayed", "earningsOverview": "Earnings Overview", "bookingsOverview": "Bookings Overview", "last6Months": "Last 6 Months", "guestComment": "Guest Comment", "dates": "Dates", "nights": "Nights", "noData": "No data available", "enterSearchTerm": "Enter search term...", "noSearchResults": "No results found", "noTitle": "No title", "noDescription": "No description", "tryDifferentSearch": "Try searching with different words", "searchResults": "Search Results", "noBookingsMessage": "No recent bookings", "noReviewsMessage": "No recent reviews", "hostModeDescription": "Enable host mode to manage your properties", "logoutConfirmation": "Are you sure you want to logout?", "mustLogin": "You must login", "mustLoginDescription": "Please login to view your profile", "login": "<PERSON><PERSON>", "confirmReservation": "Confirm Reservation", "unitDetails": "Unit Details", "unitName": "Unit Name", "numberOfDays": "Number of Days", "reservationFrom": "Reservation From", "reservationTo": "Reservation To", "priceDetails": "Price Details", "finalPrice": "Final Price", "priceBredown": "Price Breakdown", "priceType": "Price Type", "weekendPrice": "Weekend Price", "normalDays": "Normal Days", "weekendDays": "Weekend Days", "confirmBooking": "Confirm Booking", "reservationConfirmed": "Reservation confirmed successfully!", "reservationFailed": "Failed to confirm reservation", "invalidDate": "Invalid date", "notAvailable": "Not available", "days": "Days", "reviewsOverview": "Reviews Overview", "totalReviews": "Total Reviews", "allReviews": "All", "highRated": "High Rated", "noReviewsFound": "No reviews found", "noReviewsMatchFilter": "No reviews match the selected filter", "foundHelpful": "people found this review helpful", "selectReservationDate": "Select Reservation Date", "reviewReservation": "Review Reservation", "pleaseSelectBothDates": "Please select both dates", "selectedPeriodNotAvailable": "The selected period is not available, please choose another period.", "errorFetchingInfo": "An error occurred while fetching information", "failedToLoadVideo": "Failed to load video", "gatherPoint": "Gather Point", "muteVideo": "Mute Video", "unmuteVideo": "Unmute Video", "playVideo": "Play Video", "pauseVideo": "Pause Video", "removeFromFavorites": "Remove from Favorites", "comment": "Comment", "share": "Share", "comments": "Comments", "writeComment": "Write a comment...", "postComment": "Post Comment", "noComments": "No comments yet", "commentPosted": "Comment posted successfully", "commentFailed": "Failed to post comment", "deleteComment": "Delete Comment", "editComment": "Edit Comment", "replyToComment": "Reply to Comment", "showComments": "Show Comments", "hideComments": "Hide Comments", "searchReels": "Search Reels", "filterReels": "<PERSON><PERSON>", "allCategories": "All Categories", "sortBy": "Sort By", "newest": "Newest", "oldest": "Oldest", "mostLiked": "Most Liked", "mostCommented": "Most Commented", "applyFilter": "Apply Filter", "clearFilter": "Clear Filter", "noResultsFound": "No results found", "additionalSettings": "Additional Settings", "notificationSettings": "Notification Settings", "aboutApp": "About App", "filterResults": "Filter Results", "priceRange": "Price Range", "minimumRating": "Minimum Rating", "resetFilters": "Reset", "applyFilters": "Apply", "tryDifferentSearchCriteria": "Try adjusting your search criteria", "priceLowToHigh": "Price: Low to High", "priceHighToLow": "Price: High to Low", "ratingHighToLow": "Rating: High to Low", "ratingLowToHigh": "Rating: Low to High", "popular": "Popular", "privacyAndSecurity": "Privacy and security settings", "appInformation": "App information and version", "pushNotifications": "Push Notifications", "eventNotifications": "Event Notifications", "messageNotifications": "Message Notifications", "marketingNotifications": "Marketing Notifications", "enableAllNotifications": "Enable or disable all notifications", "newEventsAndUpdates": "Notifications about new events and updates", "newMessagesAndChats": "Notifications for new messages and conversations", "offersAndMarketing": "Notifications about offers and marketing news", "testNotification": "Test Notification", "sendTestNotification": "Send Test Notification", "notificationPermissionRequired": "Notification Permission Required", "enableNotificationsInSettings": "Please enable notifications in device settings", "openSettings": "Open Settings", "dataAndPrivacy": "Data & Privacy", "dataCollection": "Data Collection", "thirdPartySharing": "Third Party Sharing", "dataRetention": "Data Retention", "yourRights": "Your Rights", "contactUs": "Contact Us", "deleteAccount": "Delete Account", "dataCollectionDesc": "How we collect and use your data", "thirdPartySharingDesc": "Information about data sharing with partners", "dataRetentionDesc": "How long we keep your data", "yourRightsDesc": "Your privacy rights and how to exercise them", "contactUsDesc": "Get in touch with our support team", "deleteAccountDesc": "Permanently delete your account and data", "proceedWithGoogle": "Continue with Google", "proceedWithApple": "Continue with Apple", "or": "OR", "welcomeGuest": "Welcome our guest... continue", "proceedAsGuest": "Continue as Guest", "proceedWithPhone": "Continue with your phone number", "verifyPhoneNumber": "Verify your phone number", "enterVerificationCode": "Enter verification code", "verificationCodeSent": "A 4-digit code has been sent to your phone", "proceedLabel": "Continue", "viewAllBookings": "View All Bookings", "viewAllReviews": "View All Reviews", "enterPhoneNumber": "Enter your phone number", "phoneNumberHint": "5xxxxxxxx", "pleaseEnterPhoneNumber": "Please enter phone number", "pleaseCheckPhoneNumber": "Please check phone number", "invalidPhoneNumber": "Invalid phone number", "phoneNumberRequired": "Phone number is required", "resendCode": "Resend code", "didntReceiveCode": "Didn't receive the code?", "verificationFailed": "Verification failed", "invalidCode": "Invalid code", "codeExpired": "Code expired", "continueButton": "Continue", "exploreAllCategoriesSubtitle": "Explore all available categories", "basicInformation": "Basic Information", "enterPropertyTitle": "Enter property title", "pleaseEnterPropertyTitle": "Please enter property title", "enterPropertyDescription": "Enter property description", "pleaseEnterDescription": "Please enter description", "pleaseEnterPrice": "Please enter price", "pleaseEnterValidPrice": "Please enter a valid price", "maxGuests": "<PERSON> Guests", "pleaseEnterMaxGuests": "Please enter max guests", "pleaseEnterValidNumber": "Please enter valid number", "pleaseEnterBedrooms": "Please enter bedrooms", "pleaseBathrooms": "Please enter bathrooms", "category": "Category", "pleaseSelectCategory": "Please select category", "facilities": "Facilities", "media": "Media", "mainImage": "Main Image", "video": "Video", "gallery": "Gallery", "addImages": "Add Images", "bookingRules": "Booking Rules", "enterBookingRules": "Enter booking rules", "enterCancellationRules": "Enter cancellation rules", "reviewSubmittedSuccessfully": "Review submitted successfully", "submitReview": "Submit Review", "loginRequiredForReservation": "You need to login to make a reservation. Guest users can also make reservations with limited features.", "loginRequiredForFavorites": "You need to login to add items to your favorites list.", "loginRequiredForReviews": "You need to login to write reviews and share your experience.", "guestModeInfo": "As a guest, you can browse and make reservations, but some features like favorites and reviews require an account.", "guestReservation": "Guest Reservation", "guestReservationMessage": "You are currently browsing as a guest. You can proceed with the reservation, but creating an account will give you access to more features.", "guestLimitations": "Guest Limitations:", "guestLimitationsDetails": "• Cannot save favorites\n• Cannot write reviews\n• Limited reservation history\n• No profile management", "loginForBetterExperience": "Login for Better Experience", "continueAsGuest": "Continue as Guest", "featureUnavailable": "Feature Unavailable", "featureRequiresLogin": "This feature requires you to login. Please create an account or login to access this feature.", "guest": "Guest", "retryConnection": "Retry Connection", "connectionError": "Connection Error", "serverError": "Server Error", "unknownError": "Unknown Error", "loadingData": "Loading data...", "refreshData": "Refresh Data", "noInternetConnection": "No Internet Connection", "checkInternetConnection": "Please check your internet connection and try again", "dataLoadFailed": "Failed to load data", "pullToRefresh": "Pull to refresh", "ofPreposition": "of", "tourismPermitNumber": "Tourism Permit Number *", "dataLoadError": "Error loading data", "noDataAvailable": "No data available", "reviewsCount": "review", "sarPerNight": "SR/night", "freeWifiArabic": "Free WiFi", "hostedBy": "Hosted by", "year": "year", "years": "years", "inHosting": "hosting", "newHost": "New host", "noDescriptionAvailable": "No description available.", "guestRating": "Guest review", "march2024": "March 2024", "sampleReviewText": "Great place to stay! Clean and comfortable and exactly as described. The host was very responsive and helpful.", "showAllReviews": "Show all", "sar": "SR", "currencySymbol": "SR", "@currencySymbol": {"description": "Currency symbol for Saudi Riyal"}, "currencyCode": "SAR", "@currencyCode": {"description": "Currency code for Saudi Riyal"}, "priceWithCurrency": "{price} SR", "@priceWithCurrency": {"description": "Price format with currency symbol", "placeholders": {"price": {"type": "String"}}}, "minimumWithdrawAmount": "Minimum withdrawal: SR 50", "@minimumWithdrawAmount": {"description": "Minimum withdrawal amount with currency"}, "smartEntry": "Smart Entry", "@smartEntry": {"description": "Label for smart entry facility in place quick stats."}, "knowledge": "Knowledge", "previousTrips": "Previous Trips", "joinAsHost": "Join as Host", "joinAsHostSubtitle": "It's easy to start hosting and earn extra income", "accountSettings": "Account <PERSON><PERSON>", "requestHelp": "Request Help", "viewProfile": "View Profile", "referHost": "Refer Host", "legal": "Legal", "previousTrip": "Previous Trip", "yearsOnAirbnb": "Years on Airbnb", "cancellationPolicyNote": "Remember that the policy set by the host suits your circumstances. In rare cases, you may be eligible for a partial or full refund according to the site's policy.", "selectCancellationPolicy": "Select Cancellation Policy", "shortTermBookings": "Short-term bookings (≤28 days)", "longTermBookings": "Long-term bookings (>28 days)", "flexiblePolicy": "Flexible", "moderatePolicy": "Moderate", "strictPolicy": "Strict", "policyDescription": "Policy Description", "pleaseSelectPropertyType": "Please select property type", "pleaseSelectCancellationPolicy": "Please select cancellation policy", "selectLocation": "Select Location", "confirmLocation": "Confirm Location", "confirm": "Confirm", "propertyPreview": "Property Preview", "publishProperty": "Publish Property", "policies": "Policies", "listView": "List View", "gridView": "Grid View", "cancelSelection": "Cancel Selection", "selectMultiple": "Select Multiple", "addNewListing": "Add New Listing", "dashboardOverview": "Dashboard Overview", "totalRevenue": "Total Revenue", "averageRating": "Average Rating", "activeListings": "Active Listings", "occupancyRate": "Occupancy Rate", "conversionRate": "Conversion Rate", "allListings": "All Listings", "inactiveListings": "Inactive Listings", "drafts": "Drafts", "pendingReview": "Pending Review", "topPerforming": "Top Performing", "needsAttention": "Needs Attention", "searchListings": "Search listings...", "clearFilters": "Clear Filters", "noListingsYet": "No Listings Yet", "noListingsDescription": "Start your hosting journey by creating your first property listing. Share your space with travelers and start earning!", "createFirstListing": "Create Your First Listing", "hostingTips": "Hosting Tips", "tip1": "Add high-quality photos to attract more guests", "tip2": "Write a detailed description of your property", "tip3": "Set competitive pricing for your area", "needHelp": "Need help?", "contactSupport": "Contact Support", "errorLoadingListings": "Error Loading Listings", "tryAgain": "Try Again", "errorPersistsContact": "If the error persists, please contact support", "listingStatus": "Listing Status", "publishListing": "Publish Listing", "editListing": "Edit Listing", "deactivate": "Deactivate", "activate": "Activate", "editWhilePending": "Edit While Pending", "rejectionReason": "Rejection Reason", "pendingReservations": "Pending Reservations", "viewReservations": "View Reservations", "activeStatusDescription": "Your listing is live and visible to guests", "inactiveStatusDescription": "Your listing is hidden from guests", "draftStatusDescription": "Your listing is saved but not published yet", "pendingStatusDescription": "Your listing is under review by our team", "suspendedStatusDescription": "Your listing has been suspended", "unknownStatusDescription": "Unknown status", "publishListingConfirmation": "Are you sure you want to publish this listing?", "deactivateListingConfirmation": "Are you sure you want to deactivate this listing?", "deactivateListing": "Deactivate Listing", "changeListingStatus": "Change Listing Status", "currentStatus": "Current Status", "selectNewStatus": "Select New Status", "changeReason": "Change Reason", "enterChangeReason": "Enter reason for status change...", "changeStatus": "Change Status", "statusChangedSuccessfully": "Status changed successfully", "statusChangeError": "Error changing status", "bulkActions": "Bulk Actions", "listingsSelected": "listings selected", "currentStatusBreakdown": "Current Status Breakdown", "selectAction": "Select Action", "applyAction": "Apply Action", "activateAll": "Activate All", "deactivateAll": "Deactivate All", "convertToDraft": "Convert to Draft", "deleteAll": "Delete All", "activateAllDescription": "Make all selected listings visible to guests", "deactivateAllDescription": "Hide all selected listings from guests", "convertToDraftDescription": "Convert all selected listings to draft status", "deleteAllDescription": "Permanently delete all selected listings", "bulkActionCompleted": "Bulk action completed successfully", "bulkActionError": "Error performing bulk action", "analyticsOverview": "Analytics Overview", "last7Days": "Last 7 Days", "last30Days": "Last 30 Days", "last90Days": "Last 90 Days", "lastYear": "Last Year", "exportData": "Export Data", "performanceGrade": "Performance Grade", "overview": "Overview", "charts": "Charts", "insights": "Insights", "details": "Details", "responseTime": "Response Time", "responseRate": "Response Rate", "viewsTrend": "Views Trend", "dailyViews": "Daily Views", "dailyBookings": "Daily Bookings", "dailyRevenue": "Daily Revenue", "bookingMetrics": "Booking Metrics", "cancelledBookings": "Cancelled Bookings", "cancellationRate": "Cancellation Rate", "revenueMetrics": "Revenue Metrics", "netRevenue": "Net Revenue", "averageDailyRate": "Average Daily Rate", "engagementMetrics": "Engagement Metrics", "uniqueViews": "Unique Views", "favoriteCount": "Favorites", "shareCount": "Shares", "analytics": "Analytics", "advancedBulkActions": "Advanced Bulk Actions", "totalValue": "Total Value", "selectActionCategory": "Select Action Category", "statusActions": "Status Actions", "pricingActions": "Pricing Actions", "managementActions": "Management Actions", "executeAction": "Execute Action", "actionParameters": "Action Parameters", "percentage": "Percentage", "newPrice": "New Price", "discountPercentage": "Discount Percentage", "discountDuration": "Discount Duration", "publishAll": "Publish All", "increasePrices": "Increase Prices", "decreasePrices": "Decrease Prices", "setPrices": "Set Prices", "applyDiscount": "Apply Discount", "duplicateAll": "Duplicate All", "exportAll": "Export All", "archiveAll": "Archive All", "publishAllDescription": "Publish all selected listings", "increasePricesDescription": "Increase prices by percentage", "decreasePricesDescription": "Decrease prices by percentage", "setPricesDescription": "Set fixed price for all listings", "applyDiscountDescription": "Apply temporary discount", "duplicateAllDescription": "Create copies of selected listings", "exportAllDescription": "Export listing data", "archiveAllDescription": "Archive selected listings", "itemsSelected": "items selected", "clearSelection": "Clear Selection", "selectAll": "Select All", "moreActions": "More Actions", "activateSelected": "Activate Selected", "deactivateSelected": "Deactivate Selected", "deleteSelected": "Delete Selected", "activateSelectedConfirmation": "Are you sure you want to activate the selected listings?", "deactivateSelectedConfirmation": "Are you sure you want to deactivate the selected listings?", "deleteSelectedConfirmation": "Are you sure you want to delete the selected listings? This action cannot be undone.", "listingsWillBeAffected": "listings will be affected", "loadingPropertyData": "Loading property data...", "errorLoadingProperty": "Error Loading Property", "goBack": "Go Back", "propertyNotFound": "Property Not Found", "propertyNotFoundDescription": "The property you're looking for doesn't exist or has been removed.", "createNewProperty": "Create New Property", "propertyImages": "Property Images", "pleaseEnterTitle": "Please enter property title", "address": "Address", "saveAsDraft": "Save as Draft", "updateProperty": "Update Property", "propertyUpdatedSuccessfully": "Property updated successfully", "propertyTitleRequired": "Property title is required", "propertyTitleTooShort": "Property title must be at least 3 characters", "propertyDescriptionRequired": "Property description is required", "propertyDescriptionTooShort": "Property description must be at least 10 characters", "priceRequired": "Price is required", "priceInvalid": "Please enter a valid price", "priceMinimum": "Minimum price is 50 SAR per night", "categoryRequired": "Please select a category", "propertyTypeRequired": "Please select a property type", "cancellationPolicyRequired": "Please select a cancellation policy", "guestsRequired": "Number of guests is required", "guestsInvalid": "Number of guests must be between 1 and 20", "bedsRequired": "Number of bedrooms is required", "bedsInvalid": "Number of bedrooms must be between 1 and 10", "bathsRequired": "Number of bathrooms is required", "bathsInvalid": "Number of bathrooms must be between 1 and 10", "facilitiesRequired": "Please select at least one facility", "locationRequired": "Please select a location", "imagesRequired": "Please add at least one image", "imagesMinimum": "Please add at least 3 images", "categoryAndType": "Category & Type", "locationAndAddress": "Location & Address", "photosAndVideo": "Photos & Video", "reviewAndSubmit": "Review & Submit", "savingProperty": "Saving property...", "validationFailed": "Please fix the errors and try again", "basicInformationDesc": "Tell us about your property", "propertyTitleHint": "Enter a catchy title for your property", "propertyDescriptionHint": "Describe your property in detail", "priceHint": "100", "priceGuidance": "Tip: Research similar properties in your area to set competitive pricing", "locationSelected": "Location Selected", "changeLocation": "Change Location", "propertyPhotos": "Property Photos", "propertyVideoOptional": "Property Video (Optional)", "addPhotos": "Add Photos", "addVideo": "Add Video", "changeVideo": "Change Video", "takePhoto": "Take Photo", "chooseFromGallery": "Choose from Gallery", "chooseMultiple": "<PERSON><PERSON>", "noPhotosAdded": "No photos added yet", "videoPreview": "Video Preview", "main": "Main", "previous": "Previous", "notProvided": "Not provided", "creatingProperty": "Creating your property...", "pleaseWaitProcessing": "Please wait while we process your information", "categoryTypeDescription": "Choose the category and type that best describes your property", "bookingRulesDescription": "Set booking rules and provide tourism permit information", "bookingRulesHint": "Enter any specific rules for booking your property (optional)", "tourismPermitNumberHint": "Enter your tourism permit number (required)", "tourismPermitDocument": "Tourism Permit Document *", "tourismPermitDocumentHint": "Upload your tourism permit document (PDF, DOC, DOCX, JPG, PNG)", "documentSelected": "Document Selected", "changeDocument": "Change Document", "uploadDocument": "Upload Document", "tourismPermitInfo": "Tourism permit is required for all properties. This helps build trust with guests and ensures compliance with local regulations.", "propertyDetailsDescription": "Specify the details and amenities of your property", "tourismPermitNumberRequired": "Tourism permit number is required", "tourismPermitNumberMinLength": "Tourism permit number should be at least 5 characters", "tourismPermitDocumentRequired": "Tourism permit document is required", "bookingRulesMinLength": "Booking rules should be at least 10 characters if provided", "reviewDetails": "Review Details", "title": "Title", "bookingRulesReview": "Booking Rules", "tourismPermitNumberReview": "Tourism Permit Number", "tourismPermitDocumentReview": "Tourism Permit Document", "photos": "Photos", "notSelected": "Not selected", "noneSelected": "None selected", "unknown": "Unknown", "selected": "Selected", "uploaded": "Uploaded", "notUploaded": "Not uploaded", "added": "Added", "notAdded": "Not added", "images": "images", "addPhoto": "Add Photo", "selectFacilities": "Select Facilities", "failedToLoadPropertyTypes": "Failed to load property types", "failedToLoadCancellationPolicies": "Failed to load cancellation policies", "loadingCategories": "Loading categories...", "loadingPropertyTypes": "Loading property types...", "loadingCancellationPolicies": "Loading cancellation policies...", "loadingFacilities": "Loading facilities...", "propertyTypeOption": "Property type option", "friends": "Friends", "requests": "Requests", "searchFriends": "Search", "searchFriendsHint": "Search for friends by name or email", "searchForFriends": "Search for friends", "searchForFriendsDescription": "Type a name or email to search for new friends", "noFriendsYet": "No friends yet", "noFriendsDescription": "Start by adding new friends to connect with them", "noPendingRequests": "No pending requests", "noPendingRequestsDescription": "Incoming friend requests will appear here", "noSearchResultsDescription": "No users found with this name", "addFriend": "Add", "acceptRequest": "Accept", "declineRequest": "Decline", "removeFriend": "Remove friend", "alreadyFriends": "Friends", "requestSent": "<PERSON><PERSON>", "host": "Host", "mutualFriends": "mutual friends", "friendRequestSentSuccess": "Friend request sent successfully", "friendRequestSentError": "Error sending friend request", "friendRequestAcceptedSuccess": "Friend request accepted successfully", "friendRequestAcceptedError": "Error accepting friend request", "friendRequestDeclinedSuccess": "Friend request declined successfully", "friendRequestDeclinedError": "Error declining friend request", "friendRemovedSuccess": "Friend removed successfully", "friendRemovedError": "Error removing friend", "searchError": "Search error", "loadingFriends": "Loading friends...", "loadingRequests": "Loading requests...", "refreshFriends": "Refresh friends list", "goToPendingRequests": "Please go to the requests tab to accept the friend request", "messageFeatureInDevelopment": "Messaging feature is under development", "failedToLoadFriends": "Failed to load friends", "failedToLoadRequests": "Failed to load requests", "acquaintances": "Acquaintances", "sendMessage": "Send message", "friendRequestTime": "Friend request", "retryButton": "Retry", "minutesAgo": "minutes ago", "hoursAgo": "hours ago", "daysAgo": "days ago", "ago": "ago", "hostModeActivated": "Host mode activated successfully", "hostModeDeactivated": "Host mode deactivated successfully", "earlyAccessFeatures": "Early Access Features", "newLabel": "New", "guestUser": "Guest User", "loginForFullExperience": "Login for full experience", "jeddahSaudiArabia": "Jeddah, Saudi Arabia", "loginRequired": "<PERSON><PERSON> Required", "loginRequiredMessage": "You must login to access this feature", "switchToTravel": "Switch to Travel", "hosting": "Hosting", "editProfileTitle": "Edit Profile", "saveChangesTooltip": "Save Changes", "unexpectedError": "An unexpected error occurred", "pleaseSelectBirthdate": "Please select birthdate", "validationError": "Validation error", "accountSettingsTitle": "Account <PERSON><PERSON>", "loginRequiredForSettings": "You must login to access account settings", "accountInfo": "Account Information", "editProfileSubtitle": "Update name, photo and personal information", "emailAddress": "Email Address", "phoneNumberLabel": "Phone Number", "security": "Security", "changePassword": "Change Password", "changePasswordSubtitle": "Update your password", "twoFactorAuth": "Two-Factor Authentication", "twoFactorAuthSubtitle": "Additional security for your account", "connectedDevices": "Connected Devices", "connectedDevicesSubtitle": "Manage devices you're logged in from", "preferences": "Preferences", "notificationsSubtitle": "Manage notification settings", "locationSubtitle": "Privacy and location settings", "dangerZone": "Danger Zone", "deleteAccountSubtitle": "Permanently delete your account - cannot be undone", "deleteAccountTitle": "Delete Account", "deleteAccountConfirmMessage": "Are you sure you want to delete your account? This action cannot be undone and all your data will be lost.", "delete": "Delete", "supportCenter": "Support Center", "errorOccurred": "Error", "frequentlyAskedQuestions": "Frequently Asked Questions", "supportTickets": "Support Tickets", "newTicket": "New Ticket", "noFaqsAvailable": "No FAQs available", "noSupportTickets": "No support tickets", "ticketCreatedSuccessfully": "Ticket created successfully", "createNewSupportTicket": "Create New Support Ticket", "subject": "Subject", "pleaseEnterSubject": "Please enter subject", "descriptionLabel": "Description", "priority": "Priority", "low": "Low", "medium": "Medium", "high": "High", "urgent": "<PERSON><PERSON>", "submitTicket": "Submit Ticket", "helpAndSupport": "Help & Support", "quickHelp": "Quick Help", "howToSearch": "How to Search", "makeBooking": "Make a Booking", "paymentAndBilling": "Payment & Billing", "frequentQuestions": "Frequently Asked Questions", "howToCancelBooking": "How can I cancel a booking?", "howToCancelBookingAnswer": "You can cancel the booking by going to \"My Bookings\" and selecting the booking you want to cancel.", "refundPolicy": "What is the refund policy?", "refundPolicyAnswer": "Refund policy varies by property type and host policy. You can review the details on the booking page.", "howToChangeBooking": "How can I change booking information?", "howToChangeBookingAnswer": "You can modify some booking information by contacting the host or customer service.", "howToBecomeHost": "How do I become a host?", "howToBecomeHostAnswer": "You can switch to host mode from your profile and add your first property.", "liveChat": "Live Chat", "available24_7": "Available 24/7", "emailSupport": "Email Support", "phoneSupport": "Phone Support", "usefulResources": "Useful Resources", "userGuide": "User Guide", "tutorialVideos": "Tutorial Videos", "helpCenter": "Help Center", "viewProfileTitle": "View Profile", "loginRequiredToViewProfile": "Login required to view profile", "menu": "<PERSON><PERSON>", "switchToTravelMode": "Switch to Travel", "trips": "Trips", "aboutMe": "About Me", "noAboutMeAdded": "No personal bio added yet.", "languages": "Languages", "arabicEnglish": "Arabic, English", "locationLabel": "Location", "memberSince": "Member since", "january2023": "January 2023", "verification": "Verification", "emailVerification": "Email", "phoneVerification": "Phone Number", "identityVerification": "Identity", "manageNotificationSettings": "Manage notification settings", "privacyLocationSettings": "Privacy and location settings", "faq": "FAQ", "errorMessage": "Error", "cancelBookingAnswer": "You can cancel your booking by going to \"My Bookings\" and selecting the booking you want to cancel.", "changeBookingInfo": "How can I change booking information?", "changeBookingAnswer": "You can modify some booking information by contacting the host or customer service.", "becomeHost": "How do I become a host?", "becomeHostAnswer": "You can switch to host mode from your profile and add your first property.", "available247": "Available 24/7", "jeddahSaudi": "Jeddah, Saudi Arabia", "noBioAdded": "No bio added yet.", "rating0": "0 rating", "accountInfoTitle": "Account Information", "personalInfoTab": "Personal Information", "securityTab": "Security", "statisticsTab": "Statistics", "noInfoAvailable": "No information available", "noSecuritySettingsAvailable": "No security settings available", "noStatisticsAvailable": "No statistics available", "editPersonalInfo": "Edit Personal Information", "exportAccountData": "Export Account Data", "activeSessions": "Active Sessions", "recentActivities": "Recent Activities", "accountStatistics": "Account Statistics", "memberSinceLabel": "Member Since", "lastLogin": "Last Login", "totalBookingsLabel": "Total Bookings", "totalReviewsLabel": "Total Reviews", "verifiedAccount": "Verified Account", "unverifiedAccount": "Unverified Account", "currentSession": "Current", "dataExportedSuccessfully": "Data Exported Successfully", "dataExportSuccessMessage": "Your data has been exported successfully", "bookingsCount": "booking", "completePersonalInfo": "Complete personal information", "cannotLoadUserData": "Cannot load user data. Please try again.", "changePasswordTitle": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "enterCurrentPassword": "Please enter current password", "enterNewPassword": "Please enter new password", "passwordMinLength": "Password must be at least 8 characters", "confirmNewPasswordField": "Please confirm new password", "passwordsDoNotMatch": "Passwords do not match", "changePasswordButton": "Change Password", "passwordChangedSuccessfully": "Password changed successfully", "personalInformation": "Personal Information", "hostingResources": "Hosting Resources", "findCoHost": "Find Co-Host", "createNewListing": "Create New Listing", "privacySettingsTitle": "Privacy Settings", "profilePrivacy": "Profile Privacy", "showProfile": "Show Profile", "showProfileSubtitle": "Allow others to see your profile", "showEmail": "Show Email", "showEmailSubtitle": "Display your email in profile", "showPhone": "Show Phone Number", "showPhoneSubtitle": "Display your phone number in profile", "dataAndLocation": "Data & Location", "shareLocation": "Share Location", "shareLocationSubtitle": "Allow app to access your location", "analyticsDataCollection": "Analytics Data Collection", "analyticsDataCollectionSubtitle": "Help us improve the app with anonymous data", "downloadMyData": "Download My Data", "downloadMyDataSubtitle": "Get a copy of all your data", "communication": "Communication", "allowMessages": "Allow Messages", "allowMessagesSubtitle": "Allow hosts and guests to send you messages", "pushNotificationsSubtitle": "Receive instant notifications for messages and bookings", "marketingEmails": "Marketing Emails", "marketingEmailsSubtitle": "Receive email messages about offers and news", "dataManagement": "Data Management", "clearSearchHistory": "Clear Search History", "clearSearchHistorySubtitle": "Delete all previous searches", "clearCache": "<PERSON>ache", "clearCacheSubtitle": "Delete temporary files and saved images", "blockedUsers": "Blocked Users", "blockedUsersSubtitle": "Manage blocked users list", "downloadData": "Download Data", "downloadDataMessage": "A copy of all your data will be sent to your email within 24 hours.", "downloadDataSuccess": "Data download request successful", "clearSearchHistoryTitle": "Clear Search History", "clearSearchHistoryMessage": "Do you want to delete all previous searches?", "searchHistoryCleared": "Search history cleared", "clearCacheTitle": "<PERSON>ache", "clearCacheMessage": "Do you want to delete all temporary files?", "cacheCleared": "<PERSON><PERSON> cleared", "clear": "Clear", "logoutSuccessful": "Logout Successful", "chooseWhatToDoNow": "Choose what to do now", "reviewsLoadError": "Failed to load reviews", "highestRated": "Highest Rated", "lowestRated": "Lowest Rated", "minRating": "Minimum Rating", "fourPlusStars": "4+ stars", "threePlusStars": "3+ stars", "twoPlusStars": "2+ stars", "onePlusStars": "1+ stars", "noReviews": "No reviews", "beFirstToReview": "Be the first to review this property"}