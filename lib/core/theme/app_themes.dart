import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_colors.dart';

/// 🌓 Enhanced Light Theme
/// Light mode: white background, yellow primary, black accent
final ThemeData lightTheme = ThemeData(
  brightness: Brightness.light,
  scaffoldBackgroundColor: AppColors.white, // Pure white background
  cardColor: AppColors.white,
  primarySwatch: const MaterialColor(0xfffec53a, <int, Color>{
    50: Color(0xFFFFF8E1),
    100: Color(0xFFFFECB3),
    200: Color(0xFFFFE082),
    300: Color(0xFFFFD54F),
    400: Color(0xFFFFCA28),
    500: AppColors.primary,
    600: Color(0xFFFFB300),
    700: Color(0xFFFFA000),
    800: Color(0xFFFF8F00),
    900: Color(0xFFFF6F00),
  }),
  primaryColor: AppColors.primary,
  colorScheme: const ColorScheme.light(
    primary: AppColors.primary,
    secondary: AppColors.black, // Black accent for light mode
    surface: AppColors.white,
    onPrimary: AppColors.black,
    onSecondary: AppColors.white,
    onSurface: AppColors.black,
  ),
  appBarTheme: const AppBarTheme(
    backgroundColor: AppColors.white,
    foregroundColor: AppColors.black,
    iconTheme: IconThemeData(color: AppColors.black),
    titleTextStyle: TextStyle(
      color: AppColors.black,
      fontSize: 20,
      fontWeight: FontWeight.bold,
      fontFamily: 'HacenTunisia',
    ),
    elevation: 0,
    surfaceTintColor: Colors.transparent,
    shadowColor: Colors.black12,
  ),
  textTheme: const TextTheme(
    displayLarge: TextStyle(
      fontFamily: 'SuisseIntl',
      color: AppColors.black,
      fontWeight: FontWeight.bold,
      fontSize: 32,
    ),
    displayMedium: TextStyle(
      fontFamily: 'SuisseIntl',
      color: AppColors.black,
      fontWeight: FontWeight.w600,
      fontSize: 28,
    ),
    headlineLarge: TextStyle(
      fontFamily: 'SuisseIntl',
      color: AppColors.black,
      fontWeight: FontWeight.bold,
      fontSize: 24,
    ),
    headlineMedium: TextStyle(
      fontFamily: 'SuisseIntl',
      color: AppColors.black,
      fontWeight: FontWeight.w600,
      fontSize: 20,
    ),
    titleLarge: TextStyle(
      fontFamily: 'SuisseIntl',
      color: AppColors.black,
      fontWeight: FontWeight.w600,
      fontSize: 18,
    ),
    titleMedium: TextStyle(
      fontFamily: 'SuisseIntl',
      color: AppColors.black,
      fontWeight: FontWeight.w500,
      fontSize: 16,
    ),
    bodyLarge: TextStyle(
      fontFamily: 'SuisseIntl',
      color: AppColors.black,
      fontSize: 16,
    ),
    bodyMedium: TextStyle(
      fontFamily: 'SuisseIntl',
      color: AppColors.black,
      fontSize: 14,
    ),
    bodySmall: TextStyle(
      fontFamily: 'SuisseIntl',
      color: Color(0xFF666666),
      fontSize: 12,
    ),
    labelLarge: TextStyle(
      fontFamily: 'SuisseIntl',
      color: AppColors.black,
      fontWeight: FontWeight.w500,
      fontSize: 14,
    ),
  ),
  iconTheme: const IconThemeData(color: AppColors.black),
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: AppColors.yellow,
      foregroundColor: AppColors.black,
      textStyle: const TextStyle(
        fontWeight: FontWeight.bold,
        fontFamily: 'HacenTunisia',
      ),
      elevation: 2,
      shadowColor: AppColors.yellow.withValues(alpha: 0.3),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    ),
  ),
  textButtonTheme: TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: AppColors.yellow,
      textStyle: const TextStyle(
        fontWeight: FontWeight.w600,
        fontFamily: 'HacenTunisia',
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    ),
  ),
  inputDecorationTheme: InputDecorationTheme(
    filled: true,
    fillColor: AppColors.white,
    border: OutlineInputBorder(
      borderSide: BorderSide(color: Colors.grey.shade300),
      borderRadius: BorderRadius.circular(16),
    ),
    enabledBorder: OutlineInputBorder(
      borderSide: BorderSide(color: Colors.grey.shade300),
      borderRadius: BorderRadius.circular(16),
    ),
    focusedBorder: OutlineInputBorder(
      borderSide: const BorderSide(color: AppColors.yellow, width: 2),
      borderRadius: BorderRadius.circular(16),
    ),
    errorBorder: OutlineInputBorder(
      borderSide: const BorderSide(color: Colors.red, width: 1),
      borderRadius: BorderRadius.circular(16),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderSide: const BorderSide(color: Colors.red, width: 2),
      borderRadius: BorderRadius.circular(16),
    ),
    labelStyle: const TextStyle(
      color: AppColors.yellow,
      fontFamily: 'HacenTunisia',
    ),
    hintStyle: TextStyle(
      color: Colors.grey.shade600,
      fontFamily: 'HacenTunisia',
    ),
    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
  ),
  sliderTheme: SliderThemeData(
    activeTrackColor: AppColors.yellow,
    inactiveTrackColor: Colors.grey[300],
    thumbColor: AppColors.yellow,
    overlayColor: AppColors.yellow.withValues(alpha: 0.3),
  ),

);

/// 🌑 Dark Theme
/// Dark mode: black background, black primary, yellow accent
final ThemeData darkTheme = ThemeData(
  brightness: Brightness.dark,
  scaffoldBackgroundColor: AppColors.black, // Pure black background
  cardColor: AppColors.darkGrey,
  appBarTheme: const AppBarTheme(
    backgroundColor: AppColors.black, // Black AppBar for dark mode
    foregroundColor: AppColors.yellow,
    iconTheme: IconThemeData(color: AppColors.yellow), // Yellow icons
    titleTextStyle: TextStyle(
      color: AppColors.yellow, // Yellow text
      fontSize: 20,
      fontWeight: FontWeight.bold,
      fontFamily: 'HacenTunisia',
    ),
    elevation: 0,
    surfaceTintColor: Colors.transparent,
    shadowColor: Colors.black54,
  ),
  textTheme: const TextTheme(
    displayLarge: TextStyle(
      fontFamily: 'SuisseIntl',
      color: AppColors.white,
      fontWeight: FontWeight.bold,
    ),
    displayMedium: TextStyle(
      fontFamily: 'SuisseIntl',
      color: AppColors.white,
    ),
    bodyLarge: TextStyle(
      fontFamily: 'SuisseIntl',
      color: AppColors.white,
    ),
    bodyMedium: TextStyle(
      fontFamily: 'SuisseIntl',
      color: AppColors.white,
    ),
    labelLarge: TextStyle(
      fontFamily: 'SuisseIntl',
      color: AppColors.white,
    ),
  ),
  iconTheme: const IconThemeData(color: AppColors.yellow), // Yellow icons for dark mode
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: AppColors.yellow,
      foregroundColor: AppColors.black,
      textStyle: const TextStyle(
        fontWeight: FontWeight.bold,
        fontFamily: 'HacenTunisia',
      ),
      elevation: 2,
      shadowColor: AppColors.yellow.withValues(alpha: 0.3),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    ),
  ),
  textButtonTheme: TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: AppColors.yellow, // Yellow text buttons for dark mode
      textStyle: const TextStyle(
        fontWeight: FontWeight.w600,
        fontFamily: 'HacenTunisia',
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    ),
  ),
  inputDecorationTheme: InputDecorationTheme(
    filled: true,
    fillColor: AppColors.darkGrey,
    border: OutlineInputBorder(
      borderSide: BorderSide(color: AppColors.yellow.withValues(alpha: 0.8)),
      borderRadius: BorderRadius.circular(16),
    ),
    enabledBorder: OutlineInputBorder(
      borderSide: BorderSide(color: AppColors.yellow.withValues(alpha: 0.6)),
      borderRadius: BorderRadius.circular(16),
    ),
    focusedBorder: OutlineInputBorder(
      borderSide: const BorderSide(color: AppColors.yellow, width: 2),
      borderRadius: BorderRadius.circular(16),
    ),
    errorBorder: OutlineInputBorder(
      borderSide: const BorderSide(color: Colors.red, width: 1),
      borderRadius: BorderRadius.circular(16),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderSide: const BorderSide(color: Colors.red, width: 2),
      borderRadius: BorderRadius.circular(16),
    ),
    labelStyle: const TextStyle(
      color: AppColors.yellow,
      fontFamily: 'HacenTunisia',
    ),
    hintStyle: const TextStyle(
      color: Colors.grey,
      fontFamily: 'HacenTunisia',
    ),
    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
  ),
  sliderTheme: SliderThemeData(
    activeTrackColor: AppColors.primary,
    inactiveTrackColor: Colors.grey[600],
    thumbColor: AppColors.primary,
    overlayColor: AppColors.primary.withValues(alpha: 0.3),
  ),
  colorScheme: const ColorScheme.dark().copyWith(
    primary: AppColors.black, // Black primary for dark mode
    secondary: AppColors.primary, // Yellow accent for dark mode
    surface: AppColors.darkGrey,
    onPrimary: AppColors.primary,
    onSecondary: AppColors.black,
  ),
);
